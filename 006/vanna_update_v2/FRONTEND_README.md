# Vanna Text2SQL 前端界面

## 项目概述

这是一个基于 Gemini 聊天风格的 Text2SQL 前端界面，使用 React + Tailwind CSS 实现，提供现代化的用户体验。

## 功能特性

### 🤖 智能聊天界面
- **Gemini 风格设计**：采用 Google Gemini 的聊天界面设计风格
- **实时对话**：支持自然语言输入，AI 自动生成 SQL 查询
- **消息动画**：流畅的消息显示动画效果
- **打字指示器**：显示 AI 正在处理的状态

### 📊 数据可视化
- **SQL 查询展示**：语法高亮显示生成的 SQL 代码
- **表格数据展示**：清晰的表格形式展示查询结果
- **图表生成**：自动生成 Plotly 交互式图表
- **数据下载**：支持 CSV 格式数据下载

### 🎯 智能建议
- **预设问题**：提供常用查询问题建议
- **后续问题**：基于当前结果生成相关问题
- **历史记录**：保存和管理查询历史

### ⚙️ 管理功能
- **侧边栏管理**：历史记录和训练数据管理
- **训练数据**：查看和管理 AI 训练数据
- **响应式设计**：支持桌面和移动设备

## 技术栈

- **前端框架**：React 18
- **样式框架**：Tailwind CSS
- **图表库**：Plotly.js
- **语法高亮**：Highlight.js
- **字体**：Google Sans
- **图标**：Font Awesome

## 文件结构

```
static/
├── index.html          # 主页面
├── css/
│   └── styles.css      # 自定义样式
└── js/
    └── app.js          # React 应用主文件
```

## 启动应用

1. **安装依赖**：
   ```bash
   pip install -r requirements.txt
   ```

2. **启动服务器**：
   ```bash
   python app.py
   ```

3. **访问应用**：
   打开浏览器访问 `http://localhost:8000`

## 使用说明

### 基本使用流程

1. **开始对话**：
   - 在输入框中输入自然语言问题
   - 例如："显示所有客户的信息"、"查询销售额最高的10首歌曲"

2. **查看结果**：
   - AI 会自动生成对应的 SQL 查询
   - 点击"执行查询"按钮运行 SQL
   - 查看表格形式的查询结果

3. **数据可视化**：
   - 点击"生成图表"按钮创建可视化图表
   - 支持柱状图、折线图等多种图表类型

4. **后续操作**：
   - 下载 CSV 格式的数据
   - 查看 AI 生成的后续问题建议
   - 继续深入分析

### 预设问题示例

- 显示所有客户的信息
- 查询销售额最高的10首歌曲
- 统计每个国家的客户数量
- 显示最受欢迎的音乐类型
- 查询员工的销售业绩
- 显示专辑和艺术家信息
- 统计每年的销售总额
- 查询客户的购买历史

### 侧边栏功能

1. **历史记录**：
   - 查看之前的查询历史
   - 点击历史记录快速重新加载

2. **训练数据管理**：
   - 查看当前的训练数据
   - 删除不需要的训练数据

## API 接口

应用使用以下 API 接口：

- `GET /api/v0/generate_questions` - 获取建议问题
- `GET /api/v0/generate_sql` - 生成 SQL 查询
- `GET /api/v0/run_sql` - 执行 SQL 查询
- `GET /api/v0/generate_plotly_figure` - 生成图表
- `GET /api/v0/generate_followup_questions` - 生成后续问题
- `GET /api/v0/download_csv` - 下载 CSV 数据
- `GET /api/v0/get_training_data` - 获取训练数据
- `POST /api/v0/train` - 添加训练数据
- `POST /api/v0/remove_training_data` - 删除训练数据

## 设计特色

### Gemini 风格元素

1. **色彩方案**：
   - 主色调：蓝色 (#1a73e8)
   - 背景色：浅灰色 (#f8f9fa)
   - 文字色：深灰色 (#202124)

2. **交互设计**：
   - 圆角设计：统一使用圆角元素
   - 悬停效果：按钮和卡片的悬停动画
   - 阴影效果：适度的阴影增加层次感

3. **布局设计**：
   - 居中对话：消息居中显示，最大宽度限制
   - 响应式布局：适配不同屏幕尺寸
   - 清晰层次：明确的信息层次结构

### 用户体验优化

1. **加载状态**：
   - 打字指示器显示 AI 思考状态
   - 按钮禁用状态防止重复提交

2. **错误处理**：
   - 友好的错误提示信息
   - 网络错误自动重试机制

3. **性能优化**：
   - 图表懒加载
   - 消息虚拟滚动（大量消息时）

## 自定义配置

### 修改主题色彩

在 `index.html` 中修改 Tailwind 配置：

```javascript
tailwind.config = {
    theme: {
        extend: {
            colors: {
                'gemini-blue': '#1a73e8',    // 主色调
                'gemini-light': '#f8f9fa',   // 浅色背景
                'gemini-dark': '#202124'     // 深色文字
            }
        }
    }
}
```

### 添加新的预设问题

在 `database.py` 的 `generate_questions()` 方法中添加：

```python
def generate_questions(self) -> List[str]:
    return [
        "你的新问题1",
        "你的新问题2",
        # ... 更多问题
    ]
```

## 浏览器兼容性

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 注意事项

1. **数据库连接**：确保 SQLite 数据库文件存在且可访问
2. **网络环境**：需要访问 CDN 资源（字体、图标等）
3. **JavaScript 支持**：需要启用 JavaScript 才能正常使用

## 故障排除

### 常见问题

1. **应用无法启动**：
   - 检查 Python 依赖是否完整安装
   - 确认端口 8000 未被占用

2. **图表不显示**：
   - 检查 Plotly.js 是否正确加载
   - 查看浏览器控制台错误信息

3. **样式异常**：
   - 确认 Tailwind CSS 正确加载
   - 检查网络连接是否正常

### 调试模式

在浏览器开发者工具中查看：
- Console：JavaScript 错误信息
- Network：API 请求状态
- Elements：DOM 结构和样式

## 更新日志

### v1.0.0 (2024-05-26)
- 初始版本发布
- 实现基本的 Text2SQL 聊天功能
- 支持数据可视化和下载
- 添加历史记录和训练数据管理

---

**开发者**：Augment Agent  
**技术支持**：如有问题请查看控制台日志或联系开发团队
