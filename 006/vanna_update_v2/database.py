import os
import pandas as pd
import sqlite3
from typing import List, Dict, Any

# 简化版本的SQLiteDatabase类，用于演示
class SQLiteDatabase:
    def __init__(self, **kwargs):
        self.kwargs = kwargs
        self.db_path = None
        self.connection = None

    def connect_to_sqlite(self, url_or_path: str):
        """连接到SQLite数据库"""
        if url_or_path.startswith('http'):
            # 如果是URL，下载到本地
            import urllib.request
            self.db_path = 'Chinook.sqlite'
            if not os.path.exists(self.db_path):
                urllib.request.urlretrieve(url_or_path, self.db_path)
        else:
            self.db_path = url_or_path

        self.connection = sqlite3.connect(self.db_path)

    def run_sql(self, sql: str) -> pd.DataFrame:
        """执行SQL查询"""
        if not self.connection:
            raise Exception("Database not connected")
        return pd.read_sql_query(sql, self.connection)

    def generate_questions(self) -> List[str]:
        """生成示例问题"""
        return [
            "显示所有客户的信息",
            "查询销售额最高的10首歌曲",
            "统计每个国家的客户数量",
            "显示最受欢迎的音乐类型",
            "查询员工的销售业绩",
            "显示专辑和艺术家信息",
            "统计每年的销售总额",
            "查询客户的购买历史"
        ]

    def generate_sql(self, question: str) -> str:
        """根据问题生成SQL（简化版本）"""
        # 这里是一个简化的实现，实际应用中会使用AI模型
        question_lower = question.lower()

        if "客户" in question and "信息" in question:
            return "SELECT * FROM Customer LIMIT 10;"
        elif "销售额" in question and "歌曲" in question:
            return """SELECT t.Name as TrackName, SUM(il.UnitPrice * il.Quantity) as TotalSales
                     FROM Track t
                     JOIN InvoiceLine il ON t.TrackId = il.TrackId
                     GROUP BY t.TrackId, t.Name
                     ORDER BY TotalSales DESC
                     LIMIT 10;"""
        elif "国家" in question and "客户数量" in question:
            return """SELECT Country, COUNT(*) as CustomerCount
                     FROM Customer
                     GROUP BY Country
                     ORDER BY CustomerCount DESC;"""
        elif "音乐类型" in question or "类型" in question:
            return """SELECT g.Name as Genre, COUNT(il.InvoiceLineId) as Popularity
                     FROM Genre g
                     JOIN Track t ON g.GenreId = t.GenreId
                     JOIN InvoiceLine il ON t.TrackId = il.TrackId
                     GROUP BY g.GenreId, g.Name
                     ORDER BY Popularity DESC;"""
        elif "员工" in question and "销售" in question:
            return """SELECT e.FirstName || ' ' || e.LastName as EmployeeName,
                            COUNT(i.InvoiceId) as SalesCount,
                            SUM(i.Total) as TotalSales
                     FROM Employee e
                     LEFT JOIN Customer c ON e.EmployeeId = c.SupportRepId
                     LEFT JOIN Invoice i ON c.CustomerId = i.CustomerId
                     GROUP BY e.EmployeeId
                     ORDER BY TotalSales DESC;"""
        elif "专辑" in question and "艺术家" in question:
            return """SELECT ar.Name as ArtistName, al.Title as AlbumTitle
                     FROM Artist ar
                     JOIN Album al ON ar.ArtistId = al.ArtistId
                     ORDER BY ar.Name, al.Title
                     LIMIT 20;"""
        elif "年" in question and "销售" in question:
            return """SELECT strftime('%Y', InvoiceDate) as Year,
                            SUM(Total) as TotalSales
                     FROM Invoice
                     GROUP BY strftime('%Y', InvoiceDate)
                     ORDER BY Year;"""
        elif "购买历史" in question:
            return """SELECT c.FirstName || ' ' || c.LastName as CustomerName,
                            i.InvoiceDate, i.Total
                     FROM Customer c
                     JOIN Invoice i ON c.CustomerId = i.CustomerId
                     ORDER BY i.InvoiceDate DESC
                     LIMIT 20;"""
        else:
            return "SELECT * FROM Customer LIMIT 5;"  # 默认查询

    def generate_plotly_code(self, question: str, sql: str, df_metadata: str) -> str:
        """生成Plotly图表代码"""
        # 简化的图表代码生成
        if "国家" in question:
            return """import plotly.express as px
fig = px.bar(df, x='Country', y='CustomerCount', title='各国客户数量')"""
        elif "销售额" in question:
            return """import plotly.express as px
fig = px.bar(df, x='TrackName', y='TotalSales', title='歌曲销售额排行')"""
        elif "年" in question:
            return """import plotly.express as px
fig = px.line(df, x='Year', y='TotalSales', title='年度销售趋势')"""
        else:
            return """import plotly.express as px
fig = px.bar(df, x=df.columns[0], y=df.columns[1] if len(df.columns) > 1 else df.columns[0])"""

    def get_plotly_figure(self, plotly_code: str, df: pd.DataFrame, dark_mode: bool = False):
        """执行Plotly代码并返回图表"""
        import plotly.express as px
        import plotly.graph_objects as go

        try:
            # 创建一个安全的执行环境
            local_vars = {'df': df, 'px': px, 'go': go}
            exec(plotly_code, {"__builtins__": {}}, local_vars)
            fig = local_vars.get('fig')

            if fig:
                if dark_mode:
                    fig.update_layout(template='plotly_dark')
                return fig
        except Exception as e:
            print(f"Error generating plot: {e}")

        # 如果出错，返回一个简单的图表
        if len(df.columns) >= 2:
            fig = px.bar(df.head(10), x=df.columns[0], y=df.columns[1])
        else:
            fig = px.bar(x=range(len(df)), y=df.iloc[:, 0].head(10))
        return fig

    def generate_followup_questions(self, question: str, sql: str, df: pd.DataFrame) -> List[str]:
        """生成后续问题"""
        return [
            "能否显示更详细的信息？",
            "这个结果的趋势如何？",
            "能否按不同维度分析？",
            "相关的其他数据是什么？"
        ]

    def get_training_data(self) -> pd.DataFrame:
        """获取训练数据"""
        # 从内存中获取训练数据
        if not hasattr(self, '_training_data'):
            self._training_data = []

        if not self._training_data:
            return pd.DataFrame({
                'id': [],
                'question': [],
                'sql': [],
                'ddl': [],
                'documentation': []
            })

        return pd.DataFrame(self._training_data)

    def train(self, question: str = None, sql: str = None, ddl: str = None, documentation: str = None) -> str:
        """添加训练数据"""
        import uuid

        if not hasattr(self, '_training_data'):
            self._training_data = []

        training_id = str(uuid.uuid4())
        training_item = {
            'id': training_id,
            'question': question or '',
            'sql': sql or '',
            'ddl': ddl or '',
            'documentation': documentation or ''
        }

        self._training_data.append(training_item)
        print(f"Added training data: {training_item}")
        return training_id

    def remove_training_data(self, id: str) -> bool:
        """删除训练数据"""
        if not hasattr(self, '_training_data'):
            self._training_data = []
            return False

        original_length = len(self._training_data)
        self._training_data = [item for item in self._training_data if item['id'] != id]

        return len(self._training_data) < original_length

    async def train_init_data(self):
        """
        数据库表的结构化信息训练到向量数据库（演示版本）
        """
        pass
