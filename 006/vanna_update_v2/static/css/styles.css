/* 增强的自定义样式 */

/* 平滑滚动 */
html {
    scroll-behavior: smooth;
}

/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 文本截断 */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 毛玻璃效果增强 */
.glass-effect-strong {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.25);
}

/* 渐变动画背景 */
.gradient-bg-animated {
    background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
    animation: gradient-shift 15s ease infinite;
}

@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 代码块样式增强 */
pre code {
    font-family: 'Fira Code', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    line-height: 1.5;
}

/* 表格样式增强 */
.data-table {
    border-collapse: separate;
    border-spacing: 0;
}

.data-table th {
    position: sticky;
    top: 0;
    background: #f8f9fa;
    z-index: 10;
}

.data-table tr:hover {
    background-color: #f8f9fa;
}

/* 按钮悬停效果 */
.btn-hover-scale {
    transition: transform 0.2s ease-in-out;
}

.btn-hover-scale:hover {
    transform: scale(1.05);
}

/* 加载动画 */
.loading-dots::after {
    content: '';
    animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
    0%, 20% { content: '.'; }
    40% { content: '..'; }
    60%, 100% { content: '...'; }
}

/* 消息气泡阴影 */
.message-shadow {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 渐变背景 */
.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 图表容器样式 */
.chart-container {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 100% !important;
    }

    .message-bubble {
        max-width: 90% !important;
    }

    .input-area {
        padding: 12px !important;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .auto-dark {
        background-color: #1f2937;
        color: #f9fafb;
    }

    .auto-dark .bg-white {
        background-color: #374151;
    }

    .auto-dark .text-gray-800 {
        color: #f9fafb;
    }

    .auto-dark .border-gray-200 {
        border-color: #4b5563;
    }
}

/* 特殊效果 */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

/* 打字机效果 */
.typewriter {
    overflow: hidden;
    border-right: 2px solid #1a73e8;
    white-space: nowrap;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: #1a73e8; }
}

/* 工具提示样式 */
.tooltip {
    position: relative;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
}

.tooltip:hover::before {
    opacity: 1;
}

/* 状态指示器 */
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-online {
    background-color: #10b981;
    animation: pulse-green 2s infinite;
}

.status-offline {
    background-color: #ef4444;
}

.status-loading {
    background-color: #f59e0b;
    animation: pulse-yellow 1s infinite;
}

@keyframes pulse-green {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes pulse-yellow {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}
