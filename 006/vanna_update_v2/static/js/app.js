const { useState, useEffect, useRef } = React;

// API服务类
class ApiService {
    constructor() {
        this.baseUrl = '/api/v0';
    }

    async request(endpoint, options = {}) {
        try {
            const response = await fetch(`${this.baseUrl}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    // 生成建议问题
    async generateQuestions() {
        return this.request('/generate_questions');
    }

    // 生成SQL
    async generateSql(question) {
        return this.request(`/generate_sql?question=${encodeURIComponent(question)}`);
    }

    // 执行SQL
    async runSql(id) {
        return this.request(`/run_sql?id=${id}`);
    }

    // 生成图表
    async generatePlotlyFigure(id) {
        return this.request(`/generate_plotly_figure?id=${id}`);
    }

    // 生成后续问题
    async generateFollowupQuestions(id) {
        return this.request(`/generate_followup_questions?id=${id}`);
    }

    // 获取问题历史
    async getQuestionHistory() {
        return this.request('/get_question_history');
    }

    // 获取训练数据
    async getTrainingData() {
        return this.request('/get_training_data');
    }

    // 添加训练数据
    async addTrainingData(data) {
        return this.request('/train', {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // 删除训练数据
    async removeTrainingData(id) {
        return this.request('/remove_training_data', {
            method: 'POST',
            body: JSON.stringify({ id })
        });
    }

    // 下载CSV
    getDownloadCsvUrl(id) {
        return `${this.baseUrl}/download_csv?id=${id}`;
    }
}

// 消息气泡组件
const MessageBubble = ({ message, isUser }) => {
    const [showDetails, setShowDetails] = useState(false);

    const renderContent = () => {
        if (isUser) {
            return (
                <div className="text-white font-medium">
                    <div className="flex items-center mb-2">
                        <i className="fas fa-user-circle text-lg mr-2"></i>
                        <span className="text-sm opacity-90">您的问题</span>
                    </div>
                    <div className="text-base">{message.content}</div>
                </div>
            );
        }

        switch (message.type) {
            case 'question_list':
                return (
                    <div className="space-y-4">
                        <div className="flex items-center mb-4">
                            <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full flex items-center justify-center mr-3">
                                <i className="fas fa-magic text-white"></i>
                            </div>
                            <div>
                                <div className="text-white font-semibold text-lg">{message.header}</div>
                                <div className="text-white/70 text-sm">选择一个问题开始您的数据探索之旅</div>
                            </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            {message.questions.map((question, index) => (
                                <button
                                    key={index}
                                    className="feature-card p-4 rounded-xl text-left transition-all duration-300 hover:scale-105 group"
                                    onClick={() => window.app.handleQuestionClick(question)}
                                >
                                    <div className="flex items-start">
                                        <i className="fas fa-database text-accent-400 mr-3 mt-1 group-hover:text-accent-300 transition-colors"></i>
                                        <div className="text-white font-medium text-sm leading-relaxed">{question}</div>
                                    </div>
                                </button>
                            ))}
                        </div>
                    </div>
                );

            case 'sql':
                return (
                    <div className="space-y-4">
                        <div className="flex items-center mb-3">
                            <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mr-3">
                                <i className="fas fa-code text-white text-sm"></i>
                            </div>
                            <div className="text-white font-semibold">AI 生成的 SQL 查询</div>
                        </div>
                        <div className="sql-code">
                            <pre className="text-sm leading-relaxed">
                                <code>{message.text}</code>
                            </pre>
                        </div>
                        <button
                            className="bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
                            onClick={() => window.app.handleRunSql(message.id)}
                        >
                            <i className="fas fa-play mr-2"></i>
                            执行查询
                        </button>
                    </div>
                );

            case 'df':
                const data = JSON.parse(message.df);
                return (
                    <div className="space-y-4">
                        <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center">
                                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mr-3">
                                    <i className="fas fa-table text-white text-sm"></i>
                                </div>
                                <div className="text-white font-semibold">查询结果</div>
                            </div>
                            <div className="flex space-x-2">
                                <button
                                    className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 transform hover:scale-105"
                                    onClick={() => window.app.handleDownloadCsv(message.id)}
                                >
                                    <i className="fas fa-download mr-2"></i>
                                    下载 CSV
                                </button>
                                <button
                                    className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 transform hover:scale-105"
                                    onClick={() => window.app.handleGenerateChart(message.id)}
                                >
                                    <i className="fas fa-chart-line mr-2"></i>
                                    生成图表
                                </button>
                            </div>
                        </div>
                        <div className="data-table bg-white/95">
                            <table className="min-w-full">
                                <thead className="bg-dark-800">
                                    <tr>
                                        {data.length > 0 && Object.keys(data[0]).map(key => (
                                            <th key={key} className="px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider">
                                                {key}
                                            </th>
                                        ))}
                                    </tr>
                                </thead>
                                <tbody className="divide-y divide-gray-200">
                                    {data.map((row, index) => (
                                        <tr key={index} className="hover:bg-gray-50 transition-colors">
                                            {Object.values(row).map((value, cellIndex) => (
                                                <td key={cellIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                                                    {value}
                                                </td>
                                            ))}
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                );

            case 'plotly_figure':
                return (
                    <div className="space-y-4">
                        <div className="flex items-center mb-3">
                            <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mr-3">
                                <i className="fas fa-chart-bar text-white text-sm"></i>
                            </div>
                            <div className="text-white font-semibold">数据可视化</div>
                        </div>
                        <div className="bg-white/95 rounded-xl p-4">
                            <div id={`plot-${message.id}`} className="w-full h-96"></div>
                        </div>
                    </div>
                );

            case 'error':
                return (
                    <div className="bg-red-500/20 border border-red-400/30 rounded-xl p-4">
                        <div className="flex items-center">
                            <i className="fas fa-exclamation-triangle text-red-400 mr-3"></i>
                            <div className="text-white font-medium">{message.content}</div>
                        </div>
                    </div>
                );

            default:
                return <div className="text-white">{message.content}</div>;
        }
    };

    useEffect(() => {
        if (message.type === 'plotly_figure' && message.fig) {
            setTimeout(() => {
                try {
                    const plotDiv = document.getElementById(`plot-${message.id}`);
                    if (plotDiv) {
                        Plotly.newPlot(plotDiv, JSON.parse(message.fig), {
                            paper_bgcolor: 'rgba(0,0,0,0)',
                            plot_bgcolor: 'rgba(0,0,0,0)',
                            font: { family: 'Inter, sans-serif' }
                        }, {responsive: true});
                    }
                } catch (error) {
                    console.error('Error rendering plot:', error);
                }
            }, 100);
        }
    }, [message]);

    return (
        <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-6 animate-slide-up`}>
            <div className={`max-w-5xl ${
                isUser
                    ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white message-bubble'
                    : 'glass-effect text-white'
            } rounded-2xl px-6 py-4 shadow-2xl`}>
                {renderContent()}
            </div>
        </div>
    );
};

// 输入区域组件
const InputArea = ({ onSendMessage, isLoading }) => {
    const [input, setInput] = useState('');
    const textareaRef = useRef(null);

    const handleSubmit = (e) => {
        e.preventDefault();
        if (input.trim() && !isLoading) {
            onSendMessage(input.trim());
            setInput('');
        }
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSubmit(e);
        }
    };

    useEffect(() => {
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
        }
    }, [input]);

    return (
        <div className="glass-effect border-t border-white/20 p-6">
            <form onSubmit={handleSubmit} className="max-w-5xl mx-auto">
                <div className="flex items-end space-x-4">
                    <div className="flex-1 relative">
                        <div className="absolute left-4 top-4 text-white/60">
                            <i className="fas fa-comment-dots"></i>
                        </div>
                        <textarea
                            ref={textareaRef}
                            value={input}
                            onChange={(e) => setInput(e.target.value)}
                            onKeyPress={handleKeyPress}
                            placeholder="请输入您的数据查询问题，例如：显示所有客户的订单数量、统计每个国家的销售额..."
                            className="w-full resize-none bg-white/10 border border-white/20 rounded-2xl pl-12 pr-16 py-4 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 max-h-32 backdrop-blur-sm"
                            rows="1"
                            disabled={isLoading}
                        />
                        <button
                            type="submit"
                            disabled={!input.trim() || isLoading}
                            className="absolute right-3 bottom-3 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-xl w-10 h-10 flex items-center justify-center transition-all duration-300 transform hover:scale-105 shadow-lg"
                        >
                            {isLoading ? (
                                <i className="fas fa-spinner fa-spin"></i>
                            ) : (
                                <i className="fas fa-paper-plane"></i>
                            )}
                        </button>
                    </div>
                </div>
                <div className="mt-3 text-center">
                    <div className="text-white/60 text-xs">
                        支持中文自然语言查询 • 按 Enter 发送，Shift+Enter 换行
                    </div>
                </div>
            </form>
        </div>
    );
};

// 侧边栏组件
const Sidebar = ({ isOpen, onClose, questionHistory, trainingData, onLoadQuestion, onDeleteTrainingData }) => {
    const [activeTab, setActiveTab] = useState('history');

    return (
        <div className={`fixed inset-y-0 left-0 z-50 w-80 glass-effect shadow-2xl transform transition-transform duration-300 ${isOpen ? 'translate-x-0' : '-translate-x-full'}`}>
            <div className="flex flex-col h-full">
                <div className="flex items-center justify-between p-6 border-b border-white/20">
                    <h2 className="text-xl font-bold text-white">数据管理</h2>
                    <button
                        onClick={onClose}
                        className="text-white/70 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg"
                    >
                        <i className="fas fa-times text-lg"></i>
                    </button>
                </div>

                <div className="flex border-b border-white/20">
                    <button
                        className={`flex-1 py-4 px-4 text-sm font-semibold transition-all duration-300 ${
                            activeTab === 'history'
                                ? 'text-white bg-white/10 border-b-2 border-accent-400'
                                : 'text-white/70 hover:text-white hover:bg-white/5'
                        }`}
                        onClick={() => setActiveTab('history')}
                    >
                        <i className="fas fa-history mr-2"></i>
                        查询历史
                    </button>
                    <button
                        className={`flex-1 py-4 px-4 text-sm font-semibold transition-all duration-300 ${
                            activeTab === 'training'
                                ? 'text-white bg-white/10 border-b-2 border-accent-400'
                                : 'text-white/70 hover:text-white hover:bg-white/5'
                        }`}
                        onClick={() => setActiveTab('training')}
                    >
                        <i className="fas fa-database mr-2"></i>
                        训练数据
                    </button>
                </div>

                <div className="flex-1 overflow-y-auto p-4 scrollbar-hide">
                    {activeTab === 'history' && (
                        <div className="space-y-3">
                            {questionHistory.map((item, index) => (
                                <button
                                    key={index}
                                    className="w-full text-left p-4 bg-white/10 hover:bg-white/20 rounded-xl transition-all duration-300 transform hover:scale-105 border border-white/10"
                                    onClick={() => onLoadQuestion(item.id)}
                                >
                                    <div className="text-sm text-white font-medium truncate mb-1">{item.question}</div>
                                    <div className="text-xs text-white/60">ID: {item.id}</div>
                                </button>
                            ))}
                            {questionHistory.length === 0 && (
                                <div className="text-center text-white/60 py-12">
                                    <i className="fas fa-history text-4xl mb-4 opacity-50"></i>
                                    <div className="text-lg font-medium">暂无查询历史</div>
                                    <div className="text-sm mt-2">开始您的第一个查询吧！</div>
                                </div>
                            )}
                        </div>
                    )}

                    {activeTab === 'training' && (
                        <div className="space-y-3">
                            {trainingData.map((item, index) => (
                                <div key={index} className="bg-white/10 rounded-xl p-4 border border-white/10">
                                    <div className="text-sm text-white font-medium mb-2 line-clamp-2">
                                        {item.question || item.documentation || '训练数据项'}
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <div className="text-xs text-white/60">ID: {item.id}</div>
                                        <button
                                            className="text-red-400 hover:text-red-300 text-sm p-2 hover:bg-red-500/20 rounded-lg transition-all duration-300"
                                            onClick={() => onDeleteTrainingData(item.id)}
                                        >
                                            <i className="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            ))}
                            {trainingData.length === 0 && (
                                <div className="text-center text-white/60 py-12">
                                    <i className="fas fa-database text-4xl mb-4 opacity-50"></i>
                                    <div className="text-lg font-medium">暂无训练数据</div>
                                    <div className="text-sm mt-2">添加训练数据提高AI精度</div>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

// 主应用组件
const App = () => {
    const [messages, setMessages] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [sidebarOpen, setSidebarOpen] = useState(false);
    const [questionHistory, setQuestionHistory] = useState([]);
    const [trainingData, setTrainingData] = useState([]);
    const messagesEndRef = useRef(null);
    const apiService = new ApiService();

    // 滚动到底部
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    // 初始化
    useEffect(() => {
        loadInitialData();
    }, []);

    const loadInitialData = async () => {
        try {
            // 加载建议问题
            const questionsResponse = await apiService.generateQuestions();
            setMessages([{
                id: 'welcome',
                type: 'question_list',
                questions: questionsResponse.questions,
                header: questionsResponse.header,
                isUser: false
            }]);

            // 加载历史记录
            const historyResponse = await apiService.getQuestionHistory();
            setQuestionHistory(historyResponse.questions || []);

            // 加载训练数据
            const trainingResponse = await apiService.getTrainingData();
            const trainingDataParsed = JSON.parse(trainingResponse.df);
            setTrainingData(trainingDataParsed);
        } catch (error) {
            console.error('Failed to load initial data:', error);
            setMessages([{
                id: 'error',
                type: 'error',
                content: '加载初始数据失败，请刷新页面重试。',
                isUser: false
            }]);
        }
    };

    // 发送消息
    const handleSendMessage = async (message) => {
        const userMessage = {
            id: Date.now().toString(),
            content: message,
            isUser: true
        };

        setMessages(prev => [...prev, userMessage]);
        setIsLoading(true);

        try {
            // 生成SQL
            const sqlResponse = await apiService.generateSql(message);
            const sqlMessage = {
                ...sqlResponse,
                isUser: false
            };
            setMessages(prev => [...prev, sqlMessage]);

        } catch (error) {
            console.error('Failed to generate SQL:', error);
            const errorMessage = {
                id: Date.now().toString(),
                type: 'error',
                content: '生成SQL失败，请重试。',
                isUser: false
            };
            setMessages(prev => [...prev, errorMessage]);
        } finally {
            setIsLoading(false);
        }
    };

    // 处理问题点击
    const handleQuestionClick = (question) => {
        handleSendMessage(question);
    };

    // 执行SQL
    const handleRunSql = async (id) => {
        setIsLoading(true);
        try {
            const response = await apiService.runSql(id);
            const resultMessage = {
                ...response,
                isUser: false
            };
            setMessages(prev => [...prev, resultMessage]);

            // 生成后续问题
            setTimeout(async () => {
                try {
                    const followupResponse = await apiService.generateFollowupQuestions(id);
                    const followupMessage = {
                        ...followupResponse,
                        isUser: false
                    };
                    setMessages(prev => [...prev, followupMessage]);
                } catch (error) {
                    console.error('Failed to generate followup questions:', error);
                }
            }, 1000);

        } catch (error) {
            console.error('Failed to run SQL:', error);
            const errorMessage = {
                id: Date.now().toString(),
                type: 'error',
                content: '执行SQL失败，请检查查询语句。',
                isUser: false
            };
            setMessages(prev => [...prev, errorMessage]);
        } finally {
            setIsLoading(false);
        }
    };

    // 生成图表
    const handleGenerateChart = async (id) => {
        setIsLoading(true);
        try {
            const response = await apiService.generatePlotlyFigure(id);
            const chartMessage = {
                ...response,
                isUser: false
            };
            setMessages(prev => [...prev, chartMessage]);
        } catch (error) {
            console.error('Failed to generate chart:', error);
            const errorMessage = {
                id: Date.now().toString(),
                type: 'error',
                content: '生成图表失败，请重试。',
                isUser: false
            };
            setMessages(prev => [...prev, errorMessage]);
        } finally {
            setIsLoading(false);
        }
    };

    // 下载CSV
    const handleDownloadCsv = (id) => {
        const url = apiService.getDownloadCsvUrl(id);
        const link = document.createElement('a');
        link.href = url;
        link.download = `data_${id}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    // 加载历史问题
    const handleLoadQuestion = async (id) => {
        // 这里可以实现加载历史问题的逻辑
        console.log('Load question:', id);
        setSidebarOpen(false);
    };

    // 删除训练数据
    const handleDeleteTrainingData = async (id) => {
        try {
            await apiService.removeTrainingData(id);
            // 重新加载训练数据
            const trainingResponse = await apiService.getTrainingData();
            const trainingDataParsed = JSON.parse(trainingResponse.df);
            setTrainingData(trainingDataParsed);
        } catch (error) {
            console.error('Failed to delete training data:', error);
        }
    };

    // 暴露方法给全局使用
    window.app = {
        handleQuestionClick,
        handleRunSql,
        handleGenerateChart,
        handleDownloadCsv
    };

    return (
        <div className="min-h-screen">
            {/* 侧边栏 */}
            <Sidebar
                isOpen={sidebarOpen}
                onClose={() => setSidebarOpen(false)}
                questionHistory={questionHistory}
                trainingData={trainingData}
                onLoadQuestion={handleLoadQuestion}
                onDeleteTrainingData={handleDeleteTrainingData}
            />

            {/* 主内容区 */}
            <div className="flex flex-col min-h-screen">
                {/* 头部 */}
                <header className="glass-effect border-b border-white/20 px-6 py-4">
                    <div className="max-w-7xl mx-auto flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <button
                                onClick={() => setSidebarOpen(true)}
                                className="text-white/80 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg"
                            >
                                <i className="fas fa-bars text-lg"></i>
                            </button>
                            <div className="flex items-center space-x-3">
                                <div className="w-12 h-12 bg-gradient-to-r from-primary-400 to-accent-400 rounded-xl flex items-center justify-center animate-float">
                                    <i className="fas fa-database text-white text-xl"></i>
                                </div>
                                <div>
                                    <h1 className="text-2xl font-bold text-white">Vanna AI</h1>
                                    <p className="text-white/70 text-sm font-medium">Text2SQL 智能查询平台</p>
                                </div>
                            </div>
                        </div>
                        <div className="hidden md:flex items-center space-x-6">
                            <div className="flex items-center space-x-2 text-white/80">
                                <i className="fas fa-magic text-accent-400"></i>
                                <span className="text-sm font-medium">支持中文自然语言</span>
                            </div>
                            <div className="flex items-center space-x-2 text-white/80">
                                <i className="fas fa-chart-line text-primary-400"></i>
                                <span className="text-sm font-medium">智能数据分析</span>
                            </div>
                        </div>
                    </div>
                </header>

                {/* 消息区域 */}
                <div className="flex-1 overflow-y-auto scrollbar-hide">
                    {messages.length === 0 ? (
                        // 欢迎界面
                        <div className="min-h-full flex items-center justify-center p-6">
                            <div className="max-w-4xl mx-auto text-center">
                                <div className="mb-8">
                                    <div className="w-24 h-24 bg-gradient-to-r from-primary-400 to-accent-400 rounded-3xl flex items-center justify-center mx-auto mb-6 animate-float">
                                        <i className="fas fa-robot text-white text-4xl"></i>
                                    </div>
                                    <h2 className="text-4xl font-bold text-white mb-4 gradient-text">
                                        欢迎使用 Vanna AI
                                    </h2>
                                    <p className="text-xl text-white/80 mb-8 leading-relaxed">
                                        将您的自然语言问题转换为精确的 SQL 查询，让数据分析变得简单高效
                                    </p>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                                    <div className="feature-card p-6 rounded-2xl text-center">
                                        <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                            <i className="fas fa-language text-white text-2xl"></i>
                                        </div>
                                        <h3 className="text-lg font-semibold text-white mb-2">自然语言输入</h3>
                                        <p className="text-white/70 text-sm">支持中文问题，无需学习 SQL 语法</p>
                                    </div>
                                    <div className="feature-card p-6 rounded-2xl text-center">
                                        <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                            <i className="fas fa-chart-bar text-white text-2xl"></i>
                                        </div>
                                        <h3 className="text-lg font-semibold text-white mb-2">智能可视化</h3>
                                        <p className="text-white/70 text-sm">自动生成交互式图表，直观展示数据</p>
                                    </div>
                                    <div className="feature-card p-6 rounded-2xl text-center">
                                        <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                            <i className="fas fa-download text-white text-2xl"></i>
                                        </div>
                                        <h3 className="text-lg font-semibold text-white mb-2">数据导出</h3>
                                        <p className="text-white/70 text-sm">支持 CSV 格式导出，方便后续分析</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        // 消息列表
                        <div className="p-6">
                            <div className="max-w-6xl mx-auto">
                                {messages.map((message) => (
                                    <MessageBubble
                                        key={message.id}
                                        message={message}
                                        isUser={message.isUser}
                                    />
                                ))}
                                {isLoading && (
                                    <div className="flex justify-start mb-6 animate-slide-up">
                                        <div className="glass-effect rounded-2xl px-6 py-4 shadow-2xl">
                                            <div className="flex items-center space-x-3">
                                                <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full flex items-center justify-center">
                                                    <i className="fas fa-robot text-white text-sm"></i>
                                                </div>
                                                <div className="text-white font-medium">
                                                    AI 正在分析您的问题<span className="typing-dots"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )}
                                <div ref={messagesEndRef} />
                            </div>
                        </div>
                    )}
                </div>

                {/* 输入区域 */}
                <InputArea onSendMessage={handleSendMessage} isLoading={isLoading} />
            </div>

            {/* 侧边栏遮罩 */}
            {sidebarOpen && (
                <div
                    className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
                    onClick={() => setSidebarOpen(false)}
                />
            )}
        </div>
    );
};

// 渲染应用
ReactDOM.render(<App />, document.getElementById('app'));
