const { useState, useEffect, useRef } = React;

// API服务类
class ApiService {
    constructor() {
        this.baseUrl = '/api/v0';
    }

    async request(endpoint, options = {}) {
        try {
            const response = await fetch(`${this.baseUrl}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    // 生成建议问题
    async generateQuestions() {
        return this.request('/generate_questions');
    }

    // 生成SQL
    async generateSql(question) {
        return this.request(`/generate_sql?question=${encodeURIComponent(question)}`);
    }

    // 执行SQL
    async runSql(id) {
        return this.request(`/run_sql?id=${id}`);
    }

    // 生成图表
    async generatePlotlyFigure(id) {
        return this.request(`/generate_plotly_figure?id=${id}`);
    }

    // 生成后续问题
    async generateFollowupQuestions(id) {
        return this.request(`/generate_followup_questions?id=${id}`);
    }

    // 获取问题历史
    async getQuestionHistory() {
        return this.request('/get_question_history');
    }

    // 获取训练数据
    async getTrainingData() {
        return this.request('/get_training_data');
    }

    // 添加训练数据
    async addTrainingData(data) {
        return this.request('/train', {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // 删除训练数据
    async removeTrainingData(id) {
        return this.request('/remove_training_data', {
            method: 'POST',
            body: JSON.stringify({ id })
        });
    }

    // 下载CSV
    getDownloadCsvUrl(id) {
        return `${this.baseUrl}/download_csv?id=${id}`;
    }
}

// 消息气泡组件
const MessageBubble = ({ message, isUser }) => {
    const [showDetails, setShowDetails] = useState(false);

    const renderContent = () => {
        if (isUser) {
            return <div className="text-gray-800">{message.content}</div>;
        }

        switch (message.type) {
            case 'question_list':
                return (
                    <div className="space-y-3">
                        <div className="text-gray-800 font-medium">{message.header}</div>
                        <div className="space-y-2">
                            {message.questions.map((question, index) => (
                                <button
                                    key={index}
                                    className="block w-full text-left p-3 bg-blue-50 hover:bg-blue-100 rounded-lg border border-blue-200 transition-colors"
                                    onClick={() => window.app.handleQuestionClick(question)}
                                >
                                    <i className="fas fa-lightbulb text-blue-500 mr-2"></i>
                                    {question}
                                </button>
                            ))}
                        </div>
                    </div>
                );

            case 'sql':
                return (
                    <div className="space-y-3">
                        <div className="text-gray-800 font-medium">生成的SQL查询：</div>
                        <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                            <pre className="text-green-400 text-sm">
                                <code>{message.text}</code>
                            </pre>
                        </div>
                        <button
                            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
                            onClick={() => window.app.handleRunSql(message.id)}
                        >
                            <i className="fas fa-play mr-2"></i>
                            执行查询
                        </button>
                    </div>
                );

            case 'df':
                const data = JSON.parse(message.df);
                return (
                    <div className="space-y-3">
                        <div className="flex items-center justify-between">
                            <div className="text-gray-800 font-medium">查询结果：</div>
                            <div className="space-x-2">
                                <button
                                    className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm transition-colors"
                                    onClick={() => window.app.handleDownloadCsv(message.id)}
                                >
                                    <i className="fas fa-download mr-1"></i>
                                    下载CSV
                                </button>
                                <button
                                    className="bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded text-sm transition-colors"
                                    onClick={() => window.app.handleGenerateChart(message.id)}
                                >
                                    <i className="fas fa-chart-bar mr-1"></i>
                                    生成图表
                                </button>
                            </div>
                        </div>
                        <div className="overflow-x-auto bg-gray-50 rounded-lg">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-100">
                                    <tr>
                                        {data.length > 0 && Object.keys(data[0]).map(key => (
                                            <th key={key} className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {key}
                                            </th>
                                        ))}
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {data.map((row, index) => (
                                        <tr key={index} className="hover:bg-gray-50">
                                            {Object.values(row).map((value, cellIndex) => (
                                                <td key={cellIndex} className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                                                    {value}
                                                </td>
                                            ))}
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                );

            case 'plotly_figure':
                return (
                    <div className="space-y-3">
                        <div className="text-gray-800 font-medium">数据可视化：</div>
                        <div id={`plot-${message.id}`} className="w-full h-96 bg-gray-50 rounded-lg"></div>
                    </div>
                );

            case 'error':
                return (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div className="flex items-center">
                            <i className="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                            <div className="text-red-800">{message.content}</div>
                        </div>
                    </div>
                );

            default:
                return <div className="text-gray-800">{message.content}</div>;
        }
    };

    useEffect(() => {
        if (message.type === 'plotly_figure' && message.fig) {
            setTimeout(() => {
                try {
                    const plotDiv = document.getElementById(`plot-${message.id}`);
                    if (plotDiv) {
                        Plotly.newPlot(plotDiv, JSON.parse(message.fig), {}, {responsive: true});
                    }
                } catch (error) {
                    console.error('Error rendering plot:', error);
                }
            }, 100);
        }
    }, [message]);

    return (
        <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4 message-animation`}>
            <div className={`max-w-4xl ${isUser ? 'bg-blue-500 text-white' : 'bg-gray-100'} rounded-2xl px-4 py-3`}>
                {!isUser && (
                    <div className="flex items-center mb-2">
                        <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-2">
                            <i className="fas fa-robot text-white text-xs"></i>
                        </div>
                        <span className="text-sm font-medium text-gray-600">Vanna AI</span>
                    </div>
                )}
                {renderContent()}
            </div>
        </div>
    );
};

// 输入区域组件
const InputArea = ({ onSendMessage, isLoading }) => {
    const [input, setInput] = useState('');
    const textareaRef = useRef(null);

    const handleSubmit = (e) => {
        e.preventDefault();
        if (input.trim() && !isLoading) {
            onSendMessage(input.trim());
            setInput('');
        }
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSubmit(e);
        }
    };

    useEffect(() => {
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
        }
    }, [input]);

    return (
        <div className="border-t bg-white p-4">
            <form onSubmit={handleSubmit} className="max-w-4xl mx-auto">
                <div className="flex items-end space-x-3">
                    <div className="flex-1 relative">
                        <textarea
                            ref={textareaRef}
                            value={input}
                            onChange={(e) => setInput(e.target.value)}
                            onKeyPress={handleKeyPress}
                            placeholder="输入您的问题，例如：显示所有客户的订单数量"
                            className="w-full resize-none border border-gray-300 rounded-2xl px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent max-h-32"
                            rows="1"
                            disabled={isLoading}
                        />
                        <button
                            type="submit"
                            disabled={!input.trim() || isLoading}
                            className="absolute right-2 bottom-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white rounded-full w-8 h-8 flex items-center justify-center transition-colors"
                        >
                            {isLoading ? (
                                <i className="fas fa-spinner fa-spin text-sm"></i>
                            ) : (
                                <i className="fas fa-paper-plane text-sm"></i>
                            )}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    );
};

// 侧边栏组件
const Sidebar = ({ isOpen, onClose, questionHistory, trainingData, onLoadQuestion, onDeleteTrainingData }) => {
    const [activeTab, setActiveTab] = useState('history');

    return (
        <div className={`fixed inset-y-0 left-0 z-50 w-80 bg-white shadow-lg transform transition-transform duration-300 ${isOpen ? 'translate-x-0' : '-translate-x-full'}`}>
            <div className="flex flex-col h-full">
                <div className="flex items-center justify-between p-4 border-b">
                    <h2 className="text-lg font-semibold text-gray-800">管理面板</h2>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700 transition-colors"
                    >
                        <i className="fas fa-times"></i>
                    </button>
                </div>

                <div className="flex border-b">
                    <button
                        className={`flex-1 py-3 px-4 text-sm font-medium ${activeTab === 'history' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}
                        onClick={() => setActiveTab('history')}
                    >
                        <i className="fas fa-history mr-2"></i>
                        历史记录
                    </button>
                    <button
                        className={`flex-1 py-3 px-4 text-sm font-medium ${activeTab === 'training' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}
                        onClick={() => setActiveTab('training')}
                    >
                        <i className="fas fa-database mr-2"></i>
                        训练数据
                    </button>
                </div>

                <div className="flex-1 overflow-y-auto p-4">
                    {activeTab === 'history' && (
                        <div className="space-y-2">
                            {questionHistory.map((item, index) => (
                                <button
                                    key={index}
                                    className="w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
                                    onClick={() => onLoadQuestion(item.id)}
                                >
                                    <div className="text-sm text-gray-800 truncate">{item.question}</div>
                                    <div className="text-xs text-gray-500 mt-1">ID: {item.id}</div>
                                </button>
                            ))}
                            {questionHistory.length === 0 && (
                                <div className="text-center text-gray-500 py-8">
                                    <i className="fas fa-history text-3xl mb-2"></i>
                                    <div>暂无历史记录</div>
                                </div>
                            )}
                        </div>
                    )}

                    {activeTab === 'training' && (
                        <div className="space-y-2">
                            {trainingData.map((item, index) => (
                                <div key={index} className="bg-gray-50 rounded-lg p-3">
                                    <div className="text-sm text-gray-800 mb-2">{item.question || item.documentation || 'Training Data'}</div>
                                    <div className="flex justify-between items-center">
                                        <div className="text-xs text-gray-500">ID: {item.id}</div>
                                        <button
                                            className="text-red-500 hover:text-red-700 text-xs"
                                            onClick={() => onDeleteTrainingData(item.id)}
                                        >
                                            <i className="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            ))}
                            {trainingData.length === 0 && (
                                <div className="text-center text-gray-500 py-8">
                                    <i className="fas fa-database text-3xl mb-2"></i>
                                    <div>暂无训练数据</div>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

// 主应用组件
const App = () => {
    const [messages, setMessages] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [sidebarOpen, setSidebarOpen] = useState(false);
    const [questionHistory, setQuestionHistory] = useState([]);
    const [trainingData, setTrainingData] = useState([]);
    const messagesEndRef = useRef(null);
    const apiService = new ApiService();

    // 滚动到底部
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    // 初始化
    useEffect(() => {
        loadInitialData();
    }, []);

    const loadInitialData = async () => {
        try {
            // 加载建议问题
            const questionsResponse = await apiService.generateQuestions();
            setMessages([{
                id: 'welcome',
                type: 'question_list',
                questions: questionsResponse.questions,
                header: questionsResponse.header,
                isUser: false
            }]);

            // 加载历史记录
            const historyResponse = await apiService.getQuestionHistory();
            setQuestionHistory(historyResponse.questions || []);

            // 加载训练数据
            const trainingResponse = await apiService.getTrainingData();
            const trainingDataParsed = JSON.parse(trainingResponse.df);
            setTrainingData(trainingDataParsed);
        } catch (error) {
            console.error('Failed to load initial data:', error);
            setMessages([{
                id: 'error',
                type: 'error',
                content: '加载初始数据失败，请刷新页面重试。',
                isUser: false
            }]);
        }
    };

    // 发送消息
    const handleSendMessage = async (message) => {
        const userMessage = {
            id: Date.now().toString(),
            content: message,
            isUser: true
        };

        setMessages(prev => [...prev, userMessage]);
        setIsLoading(true);

        try {
            // 生成SQL
            const sqlResponse = await apiService.generateSql(message);
            const sqlMessage = {
                ...sqlResponse,
                isUser: false
            };
            setMessages(prev => [...prev, sqlMessage]);

        } catch (error) {
            console.error('Failed to generate SQL:', error);
            const errorMessage = {
                id: Date.now().toString(),
                type: 'error',
                content: '生成SQL失败，请重试。',
                isUser: false
            };
            setMessages(prev => [...prev, errorMessage]);
        } finally {
            setIsLoading(false);
        }
    };

    // 处理问题点击
    const handleQuestionClick = (question) => {
        handleSendMessage(question);
    };

    // 执行SQL
    const handleRunSql = async (id) => {
        setIsLoading(true);
        try {
            const response = await apiService.runSql(id);
            const resultMessage = {
                ...response,
                isUser: false
            };
            setMessages(prev => [...prev, resultMessage]);

            // 生成后续问题
            setTimeout(async () => {
                try {
                    const followupResponse = await apiService.generateFollowupQuestions(id);
                    const followupMessage = {
                        ...followupResponse,
                        isUser: false
                    };
                    setMessages(prev => [...prev, followupMessage]);
                } catch (error) {
                    console.error('Failed to generate followup questions:', error);
                }
            }, 1000);

        } catch (error) {
            console.error('Failed to run SQL:', error);
            const errorMessage = {
                id: Date.now().toString(),
                type: 'error',
                content: '执行SQL失败，请检查查询语句。',
                isUser: false
            };
            setMessages(prev => [...prev, errorMessage]);
        } finally {
            setIsLoading(false);
        }
    };

    // 生成图表
    const handleGenerateChart = async (id) => {
        setIsLoading(true);
        try {
            const response = await apiService.generatePlotlyFigure(id);
            const chartMessage = {
                ...response,
                isUser: false
            };
            setMessages(prev => [...prev, chartMessage]);
        } catch (error) {
            console.error('Failed to generate chart:', error);
            const errorMessage = {
                id: Date.now().toString(),
                type: 'error',
                content: '生成图表失败，请重试。',
                isUser: false
            };
            setMessages(prev => [...prev, errorMessage]);
        } finally {
            setIsLoading(false);
        }
    };

    // 下载CSV
    const handleDownloadCsv = (id) => {
        const url = apiService.getDownloadCsvUrl(id);
        const link = document.createElement('a');
        link.href = url;
        link.download = `data_${id}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    // 加载历史问题
    const handleLoadQuestion = async (id) => {
        // 这里可以实现加载历史问题的逻辑
        console.log('Load question:', id);
        setSidebarOpen(false);
    };

    // 删除训练数据
    const handleDeleteTrainingData = async (id) => {
        try {
            await apiService.removeTrainingData(id);
            // 重新加载训练数据
            const trainingResponse = await apiService.getTrainingData();
            const trainingDataParsed = JSON.parse(trainingResponse.df);
            setTrainingData(trainingDataParsed);
        } catch (error) {
            console.error('Failed to delete training data:', error);
        }
    };

    // 暴露方法给全局使用
    window.app = {
        handleQuestionClick,
        handleRunSql,
        handleGenerateChart,
        handleDownloadCsv
    };

    return (
        <div className="flex h-screen bg-gray-50">
            {/* 侧边栏 */}
            <Sidebar
                isOpen={sidebarOpen}
                onClose={() => setSidebarOpen(false)}
                questionHistory={questionHistory}
                trainingData={trainingData}
                onLoadQuestion={handleLoadQuestion}
                onDeleteTrainingData={handleDeleteTrainingData}
            />

            {/* 主内容区 */}
            <div className="flex-1 flex flex-col">
                {/* 头部 */}
                <header className="bg-white border-b px-4 py-3 flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                        <button
                            onClick={() => setSidebarOpen(true)}
                            className="text-gray-500 hover:text-gray-700 transition-colors"
                        >
                            <i className="fas fa-bars"></i>
                        </button>
                        <div className="flex items-center space-x-2">
                            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                <i className="fas fa-robot text-white"></i>
                            </div>
                            <h1 className="text-xl font-semibold text-gray-800">Vanna - Text2SQL 助手</h1>
                        </div>
                    </div>
                    <div className="text-sm text-gray-500">
                        基于AI的自然语言转SQL查询工具
                    </div>
                </header>

                {/* 消息区域 */}
                <div className="flex-1 overflow-y-auto p-4 scrollbar-hide">
                    <div className="max-w-4xl mx-auto">
                        {messages.map((message) => (
                            <MessageBubble
                                key={message.id}
                                message={message}
                                isUser={message.isUser}
                            />
                        ))}
                        {isLoading && (
                            <div className="flex justify-start mb-4">
                                <div className="bg-gray-100 rounded-2xl px-4 py-3">
                                    <div className="flex items-center space-x-2">
                                        <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                            <i className="fas fa-robot text-white text-xs"></i>
                                        </div>
                                        <div className="typing-indicator text-gray-600">正在处理您的请求...</div>
                                    </div>
                                </div>
                            </div>
                        )}
                        <div ref={messagesEndRef} />
                    </div>
                </div>

                {/* 输入区域 */}
                <InputArea onSendMessage={handleSendMessage} isLoading={isLoading} />
            </div>

            {/* 侧边栏遮罩 */}
            {sidebarOpen && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 z-40"
                    onClick={() => setSidebarOpen(false)}
                />
            )}
        </div>
    );
};

// 渲染应用
ReactDOM.render(<App />, document.getElementById('app'));
