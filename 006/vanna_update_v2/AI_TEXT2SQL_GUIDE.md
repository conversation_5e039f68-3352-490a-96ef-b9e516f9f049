# 🤖 AI Text2SQL 智能查询系统使用指南

## 🎯 系统概述

这是一个基于AI的Text2SQL智能查询系统，能够将自然语言问题实时转换为精确的SQL查询。系统采用现代化的毛玻璃界面设计，提供直观的用户体验。

## ✨ 核心特性

### 🧠 AI驱动的实时SQL生成
- **智能理解**：支持中文自然语言问题输入
- **实时生成**：使用AI模型动态生成SQL查询，而非预设模板
- **上下文感知**：结合数据库表结构和训练数据生成精确查询
- **安全验证**：自动检测和阻止危险的SQL操作

### 📚 训练数据管理
- **问题-SQL对训练**：添加自然语言问题和对应SQL的训练样本
- **表结构训练**：自动提取并学习数据库表结构(DDL)
- **业务文档训练**：添加业务逻辑和字段说明文档
- **一键初始化**：自动扫描数据库并添加所有表结构到训练数据

### 📊 智能数据可视化
- **AI生成图表**：根据查询结果和问题自动选择合适的图表类型
- **交互式图表**：使用Plotly生成可交互的数据可视化
- **多种图表类型**：支持柱状图、折线图、饼图等多种可视化方式

### 🔄 智能后续问题
- **AI生成建议**：基于查询结果智能生成相关的后续问题
- **深度分析**：引导用户进行更深入的数据探索
- **上下文相关**：生成与当前查询结果相关的问题

## 🚀 快速开始

### 1. 启动系统
```bash
cd 006/vanna_update_v2
python3 app.py
```

### 2. 访问界面
打开浏览器访问：`http://localhost:8000`

### 3. 初始化训练数据
1. 点击左侧菜单按钮打开侧边栏
2. 切换到"训练数据"标签
3. 点击"初始化表结构"按钮
4. 系统会自动扫描数据库并添加所有表结构信息

### 4. 开始查询
在输入框中输入自然语言问题，例如：
- "显示所有客户的信息"
- "统计每个国家的客户数量"
- "查询销售额最高的10首歌曲"

## 📖 详细功能说明

### 🎓 训练数据管理

#### 添加问题-SQL对
1. 打开侧边栏 → 训练数据 → 添加训练数据
2. 选择"问题-SQL对"标签
3. 输入自然语言问题和对应的SQL查询
4. 点击"添加训练数据"

**示例：**
- **问题**：显示所有客户的订单数量
- **SQL**：`SELECT c.CustomerName, COUNT(o.OrderID) as OrderCount FROM Customer c LEFT JOIN Order o ON c.CustomerID = o.CustomerID GROUP BY c.CustomerID;`

#### 添加表结构信息
1. 选择"表结构"标签
2. 输入CREATE TABLE语句
3. 系统会学习表的结构和字段关系

#### 添加业务文档
1. 选择"文档说明"标签
2. 输入业务逻辑、字段含义等说明
3. 帮助AI更好地理解业务场景

### 🔍 智能查询流程

#### 1. 问题理解
- AI分析自然语言问题
- 提取关键信息和查询意图
- 结合数据库表结构信息

#### 2. SQL生成
- 使用训练数据中的相关示例
- 调用AI模型生成SQL查询
- 自动验证和清理SQL语句

#### 3. 查询执行
- 安全执行生成的SQL
- 返回查询结果
- 支持CSV格式导出

#### 4. 结果可视化
- AI分析数据特征
- 自动选择合适的图表类型
- 生成交互式可视化图表

#### 5. 后续建议
- 基于查询结果生成相关问题
- 引导用户进行深度分析
- 提供数据探索建议

### 🛡️ 安全特性

#### SQL安全检查
- 自动检测危险关键词（DROP, DELETE, UPDATE等）
- 阻止潜在的破坏性操作
- 只允许SELECT查询

#### 数据保护
- 限制查询结果数量
- 防止大量数据导出
- 保护敏感信息

## 🎨 界面特色

### 现代化设计
- **毛玻璃效果**：半透明背景和模糊效果
- **渐变背景**：动态渐变色彩背景
- **流畅动画**：消息显示和交互动画
- **响应式布局**：适配桌面和移动设备

### 用户体验
- **直观操作**：简单易用的界面设计
- **实时反馈**：即时显示处理状态
- **智能提示**：贴心的操作指导
- **优雅交互**：平滑的动画过渡

## 🔧 高级配置

### 环境变量设置
在`.env`文件中配置：

```env
# AI模型配置
OPENAI_API_KEY=your_api_key
OPENAI_API_BASE=https://api.deepseek.com/v1
LLM_MODEL=deepseek-chat

# 数据库配置
MILVUS_URI=http://localhost:19530
```

### AI模型选择
支持多种AI模型：
- **DeepSeek Chat**：高性能中文理解
- **OpenAI GPT**：强大的通用能力
- **其他兼容模型**：支持OpenAI API格式的模型

## 📊 使用示例

### 基础查询
**问题**：有多少个客户？
**生成SQL**：`SELECT COUNT(*) as CustomerCount FROM Customer;`

### 聚合分析
**问题**：每个国家有多少客户？
**生成SQL**：`SELECT Country, COUNT(*) as CustomerCount FROM Customer GROUP BY Country ORDER BY CustomerCount DESC;`

### 关联查询
**问题**：显示客户的订单信息
**生成SQL**：`SELECT c.CustomerName, i.InvoiceDate, i.Total FROM Customer c JOIN Invoice i ON c.CustomerId = i.CustomerId ORDER BY i.InvoiceDate DESC;`

### 时间分析
**问题**：每年的销售趋势如何？
**生成SQL**：`SELECT strftime('%Y', InvoiceDate) as Year, SUM(Total) as TotalSales FROM Invoice GROUP BY strftime('%Y', InvoiceDate) ORDER BY Year;`

## 🎯 最佳实践

### 1. 训练数据质量
- 添加高质量的问题-SQL对
- 包含各种查询类型的示例
- 定期更新和优化训练数据

### 2. 问题表达
- 使用清晰、具体的问题描述
- 包含关键的业务术语
- 避免过于复杂的复合问题

### 3. 结果验证
- 检查生成的SQL是否符合预期
- 验证查询结果的准确性
- 必要时手动调整查询

### 4. 数据探索
- 利用后续问题进行深度分析
- 结合可视化图表理解数据
- 从不同角度分析同一数据

## 🔍 故障排除

### 常见问题

#### 1. SQL生成不准确
- **解决方案**：添加更多相关的训练数据
- **检查**：确保表结构信息完整
- **优化**：使用更具体的问题描述

#### 2. 图表显示异常
- **检查**：确保Plotly库正确加载
- **验证**：查看浏览器控制台错误信息
- **重试**：刷新页面重新生成图表

#### 3. AI模型调用失败
- **检查**：验证API密钥和网络连接
- **备用**：系统会自动使用关键词匹配备用方案
- **配置**：确认环境变量设置正确

### 性能优化

#### 1. 查询优化
- 限制查询结果数量
- 使用适当的索引
- 避免复杂的嵌套查询

#### 2. 缓存机制
- 利用查询结果缓存
- 重用相似查询的结果
- 定期清理过期缓存

## 🚀 未来发展

### 计划功能
- **多数据库支持**：MySQL、PostgreSQL等
- **高级可视化**：更多图表类型和自定义选项
- **协作功能**：查询分享和团队协作
- **API接口**：开放API供第三方集成

### 技术升级
- **向量数据库**：使用Milvus提升相似度匹配
- **模型微调**：针对特定业务场景优化模型
- **实时学习**：从用户反馈中持续学习改进

---

**开发团队**：Augment Agent  
**技术支持**：如有问题请查看日志或联系开发团队  
**更新时间**：2024-05-26
